import { Composer } from 'grammy';
import { BotContext, UserTier, TaskComplexity } from '../types/index.js';
import { generateAIResponse } from '../lib/ai.js';
import { executeConvexMutation } from '../lib/convex.js';
import { requireAuth } from '../middleware/auth.js';
import { checkTokenLimits } from '../middleware/rateLimit.js';
import { logger } from '../lib/logger.js';
import {
  validateTelegramInput,
  telegramMessageSchema,
  sanitizeTelegramText,
  aiPromptSchema,
  logSecurityEvent
} from '../lib/validation.js';

export const messagesComposer = new Composer<BotContext>();

// Store conversation history in memory (in production, use Redis or database)
const conversationHistory = new Map<
  string,
  Array<{ role: 'user' | 'assistant'; content: string }>
>();

// Handle text messages (AI chat)
messagesComposer.on('message:text', requireAuth, async (ctx) => {
  const userId = ctx.session.userId!;
  const userTier = ctx.session.tier || UserTier.FREE;
  const telegramId = ctx.from!.id.toString();

  try {
    // 🔒 Validate incoming message
    const messageValidation = validateTelegramInput(telegramMessageSchema, ctx.message);
    if (!messageValidation.success) {
      logSecurityEvent('invalid_message_format', userId, {
        error: messageValidation.error,
        telegramId
      });
      await ctx.reply('❌ Invalid message format. Please try again.');
      return;
    }

    // 🔒 Sanitize message text
    const rawMessage = ctx.message.text;
    const message = sanitizeTelegramText(rawMessage);

    // 🔒 Validate AI prompt for injection attempts
    const promptValidation = validateTelegramInput(aiPromptSchema, message);
    if (!promptValidation.success) {
      logSecurityEvent('prompt_injection_attempt', userId, {
        message: rawMessage,
        telegramId
      });
      await ctx.reply('❌ Your message contains prohibited content. Please rephrase and try again.');
      return;
    }

    logger.debug('Message validated and sanitized', {
      userId,
      originalLength: rawMessage.length,
      sanitizedLength: message.length,
    });
    // Check if user has token limits
    const hasTokens = await checkTokenLimits(ctx, 200); // Estimate 200 tokens for this interaction
    if (!hasTokens) {
      return; // checkTokenLimits already sent the error message
    }

    // Show typing indicator
    await ctx.replyWithChatAction('typing');

    // Get conversation history
    const history = conversationHistory.get(userId) || [];

    // Generate AI response
    logger.debug('Generating AI response', {
      userId,
      userTier,
      messageLength: message.length,
      historyLength: history.length,
    });

    const aiResponse = await generateAIResponse(message, userTier, history);

    // Update conversation history (keep last 20 messages)
    history.push({ role: 'user', content: message });
    history.push({ role: 'assistant', content: aiResponse.content });

    if (history.length > 20) {
      history.splice(0, 2); // Remove oldest user-assistant pair
    }

    conversationHistory.set(userId, history);

    // Track token usage
    try {
      await executeConvexMutation('telegram:trackTokenUsage', {
        userId,
        tokens: aiResponse.tokenCount,
        model: aiResponse.model,
      });
    } catch (error) {
      logger.error('Failed to track token usage', error);
    }

    // Split long responses if needed
    const maxLength = 4096; // Telegram message limit
    if (aiResponse.content.length <= maxLength) {
      await ctx.reply(aiResponse.content, {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              { text: '🔄 Continue Chat', callback_data: 'continue_chat' },
              { text: '📊 Usage', callback_data: 'view_usage' },
            ],
          ],
        },
      });
    } else {
      // Split message into chunks
      const chunks = splitMessage(aiResponse.content, maxLength);

      for (let i = 0; i < chunks.length; i++) {
        const isLast = i === chunks.length - 1;

        await ctx.reply(chunks[i], {
          parse_mode: 'Markdown',
          reply_markup: isLast
            ? {
                inline_keyboard: [
                  [
                    {
                      text: '🔄 Continue Chat',
                      callback_data: 'continue_chat',
                    },
                    { text: '📊 Usage', callback_data: 'view_usage' },
                  ],
                ],
              }
            : undefined,
        });

        // Small delay between chunks
        if (!isLast) {
          await new Promise((resolve) => setTimeout(resolve, 500));
        }
      }
    }

    logger.botMessage('ai_response', userId, aiResponse.tokenCount);
  } catch (error) {
    logger.botError(error as Error, 'AI message generation', userId);

    // Send user-friendly error message
    await ctx.reply(
      `❌ **Oops! Something went wrong**\n\n` +
        `I encountered an error while processing your message. This could be due to:\n\n` +
        `• High server load\n` +
        `• Model temporarily unavailable\n` +
        `• Network connectivity issues\n\n` +
        `Please try again in a moment. If the problem persists, contact support.`,
      {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              { text: '🔄 Try Again', callback_data: 'retry_message' },
              { text: '🆘 Support', url: 'https://bonkai.vercel.app/support' },
            ],
          ],
        },
      },
    );
  }
});

// Handle images (if user sends an image)
messagesComposer.on('message:photo', requireAuth, async (ctx) => {
  const userTier = ctx.session.tier || UserTier.FREE;

  if (userTier === UserTier.FREE) {
    await ctx.reply(
      `📸 **Image Analysis**\n\n` +
        `Image analysis is available for paid tiers.\n\n` +
        `🥉 **BRONZE+**: Basic image understanding\n` +
        `🥈 **SILVER+**: Advanced image analysis\n` +
        `💎 **DIAMOND**: Full multimodal AI capabilities\n\n` +
        `Upgrade your tier to unlock this feature!`,
      {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              {
                text: '💰 Upgrade Tier',
                url: 'https://bonkai.vercel.app/token',
              },
              { text: '🏆 View Tiers', callback_data: 'tier_benefits' },
            ],
          ],
        },
      },
    );
    return;
  }

  await ctx.reply(
    `📸 **Image Analysis Coming Soon**\n\n` +
      `I see you've sent an image! Image analysis capabilities are coming soon to BonKai AI.\n\n` +
      `For now, I can help you with:\n` +
      `• Text-based questions about blockchain\n` +
      `• Code analysis and smart contracts\n` +
      `• Trading strategies and market analysis\n` +
      `• DeFi protocols and Web3 concepts\n\n` +
      `Stay tuned for image analysis features!`,
    {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [{ text: '💬 Ask Text Question', callback_data: 'continue_chat' }],
        ],
      },
    },
  );
});

// Handle documents/files
messagesComposer.on('message:document', requireAuth, async (ctx) => {
  const userTier = ctx.session.tier || UserTier.FREE;

  await ctx.reply(
    `📄 **Document Analysis**\n\n` +
      `Document analysis features are coming soon!\n\n` +
      `Planned features:\n` +
      `📋 Smart contract auditing\n` +
      `📊 Whitepaper analysis\n` +
      `💾 Code review\n` +
      `📈 Technical documentation parsing\n\n` +
      `Your tier (${userTier}) will determine the depth of analysis available.`,
    {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '💬 Ask About It', callback_data: 'continue_chat' },
            { text: '🏆 Upgrade', url: 'https://bonkai.vercel.app/token' },
          ],
        ],
      },
    },
  );
});

// Handle stickers (fun response)
messagesComposer.on('message:sticker', async (ctx) => {
  const responses = [
    'Nice sticker! 😄',
    'I love stickers! Got any blockchain-themed ones? 🚀',
    'Stickers are fun! Want to chat about crypto instead? 💰',
    'Cool sticker! 🎉 Now, what can I help you with today?',
    "That's a great sticker! Ready to dive into some Web3 topics? 🌐",
  ];

  const randomResponse =
    responses[Math.floor(Math.random() * responses.length)];

  await ctx.reply(randomResponse, {
    reply_markup: {
      inline_keyboard: [
        [
          { text: '💬 Start Chatting', callback_data: 'continue_chat' },
          { text: '🆘 Help', callback_data: 'help' },
        ],
      ],
    },
  });
});

// Handle voice messages
messagesComposer.on('message:voice', requireAuth, async (ctx) => {
  const userTier = ctx.session.tier || UserTier.FREE;

  await ctx.reply(
    `🎤 **Voice Messages**\n\n` +
      `Voice message processing is coming soon!\n\n` +
      `Planned features for ${userTier} tier:\n` +
      `🗣️ Speech-to-text conversion\n` +
      `🤖 AI response to voice queries\n` +
      `🔊 Text-to-speech responses\n` +
      `🌐 Multi-language support\n\n` +
      `For now, please send text messages and I'll be happy to help!`,
    {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [{ text: '💬 Send Text Instead', callback_data: 'continue_chat' }],
        ],
      },
    },
  );
});

// Handle location sharing
messagesComposer.on('message:location', async (ctx) => {
  await ctx.reply(
    `📍 **Location Received**\n\n` +
      `Thanks for sharing your location! While I can't provide location-based services yet, I can help you with:\n\n` +
      `🌍 Global crypto market analysis\n` +
      `🏛️ Regulatory information by region\n` +
      `🏪 DeFi protocols available worldwide\n` +
      `💱 Local exchange recommendations\n\n` +
      `What would you like to know about?`,
    {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '💱 Exchanges', callback_data: 'continue_chat' },
            { text: '📊 Markets', callback_data: 'continue_chat' },
          ],
          [
            { text: '🏛️ Regulations', callback_data: 'continue_chat' },
            { text: '🆘 Help', callback_data: 'help' },
          ],
        ],
      },
    },
  );
});

// Continue chat callback
messagesComposer.callbackQuery('continue_chat', async (ctx) => {
  await ctx.answerCallbackQuery();

  await ctx.reply(
    `💬 **Ready to Chat!**\n\n` +
      `Ask me anything about:\n\n` +
      `🔗 **Blockchain Technology**\n` +
      `💰 **Cryptocurrency Trading**\n` +
      `🏦 **DeFi Protocols**\n` +
      `🖼️ **NFTs & Digital Assets**\n` +
      `⚡ **Solana Ecosystem**\n` +
      `📈 **Market Analysis**\n` +
      `💻 **Smart Contract Development**\n` +
      `🌐 **Web3 & dApps**\n\n` +
      `Just type your question and I'll help you out!`,
    { parse_mode: 'Markdown' },
  );
});

// Helper function to split long messages
function splitMessage(text: string, maxLength: number): string[] {
  if (text.length <= maxLength) {
    return [text];
  }

  const chunks: string[] = [];
  let currentChunk = '';
  const sentences = text.split(/[.!?]+/);

  for (const sentence of sentences) {
    const sentenceWithPunct =
      sentence + (text[text.indexOf(sentence) + sentence.length] || '');

    if ((currentChunk + sentenceWithPunct).length <= maxLength) {
      currentChunk += sentenceWithPunct;
    } else {
      if (currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = sentenceWithPunct;
      } else {
        // If a single sentence is too long, split by words
        const words = sentenceWithPunct.split(' ');
        let wordChunk = '';

        for (const word of words) {
          if ((wordChunk + ' ' + word).length <= maxLength) {
            wordChunk += (wordChunk ? ' ' : '') + word;
          } else {
            if (wordChunk) {
              chunks.push(wordChunk.trim());
              wordChunk = word;
            } else {
              // Single word is too long, just add it
              chunks.push(word);
            }
          }
        }

        if (wordChunk) {
          currentChunk = wordChunk;
        }
      }
    }
  }

  if (currentChunk) {
    chunks.push(currentChunk.trim());
  }

  return chunks.filter((chunk) => chunk.length > 0);
}
