// 🔒 Telegram Bot Input Validation and Sanitization
import { z } from 'zod';

// Malicious pattern detection for Telegram messages
const TELEGRAM_MALICIOUS_PATTERNS = [
  // Bot command injection
  /\/[a-zA-Z_]+@[a-zA-Z_]+bot/gi,
  
  // Markdown injection
  /\[.*\]\(.*javascript:.*\)/gi,
  /\[.*\]\(.*data:.*\)/gi,
  
  // HTML injection (Telegram supports some HTML)
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /javascript:/gi,
  /vbscript:/gi,
  
  // Excessive formatting (potential DoS)
  /(\*|_|`|~){20,}/g,
  
  // Suspicious URLs
  /bit\.ly|tinyurl|t\.co|goo\.gl/gi,
];

// Content validation
export function containsMaliciousPatterns(content: string): boolean {
  return TELEGRAM_MALICIOUS_PATTERNS.some(pattern => pattern.test(content));
}

export function sanitizeTelegramText(text: string): string {
  return text
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/data:/gi, '') // Remove data: protocol
    .slice(0, 4096) // Telegram message limit
    .trim();
}

// Validation schemas
export const telegramMessageSchema = z.object({
  text: z.string()
    .min(1, 'Message cannot be empty')
    .max(4096, 'Message too long for Telegram')
    .refine(
      (text) => !containsMaliciousPatterns(text),
      'Message contains prohibited content'
    ),
  chat: z.object({
    id: z.number().int(),
    type: z.enum(['private', 'group', 'supergroup', 'channel']),
  }),
  from: z.object({
    id: z.number().int().positive(),
    is_bot: z.boolean(),
    first_name: z.string().min(1).max(64),
    username: z.string().max(32).optional(),
  }),
});

export const telegramCallbackQuerySchema = z.object({
  id: z.string(),
  from: z.object({
    id: z.number().int().positive(),
    is_bot: z.boolean(),
    first_name: z.string().min(1).max(64),
  }),
  data: z.string().max(64), // Telegram callback data limit
});

export const telegramUserIdSchema = z.number()
  .int()
  .positive()
  .max(2147483647); // Telegram user ID limit

export const linkingCodeSchema = z.string()
  .length(8, 'Linking code must be 8 characters')
  .regex(/^[A-Z0-9]{8}$/, 'Invalid linking code format');

// Rate limiting validation
export const botRateLimitSchema = z.object({
  userId: z.string().min(1),
  action: z.enum(['message', 'command', 'callback', 'inline_query']),
  tier: z.enum(['FREE', 'BRONZE', 'SILVER', 'DIAMOND']),
});

// Command validation
export const botCommandSchema = z.object({
  command: z.string()
    .min(1)
    .max(32)
    .regex(/^[a-z0-9_]+$/, 'Invalid command format'),
  args: z.array(z.string().max(100)).max(10).optional(),
});

// Session validation
export const sessionDataSchema = z.object({
  userId: z.string().optional(),
  step: z.enum(['awaiting_link_code', 'awaiting_wallet', 'authenticated']).optional(),
  linkingData: z.object({
    clerkId: z.string(),
    code: z.string(),
    expiresAt: z.number(),
  }).optional(),
  lastActivity: z.number().optional(),
  tier: z.enum(['FREE', 'BRONZE', 'SILVER', 'DIAMOND']).optional(),
});

// AI prompt validation (prevent prompt injection)
export const aiPromptSchema = z.string()
  .min(1, 'Prompt cannot be empty')
  .max(2000, 'Prompt too long')
  .refine(
    (prompt) => !containsPromptInjection(prompt),
    'Prompt contains injection attempts'
  );

function containsPromptInjection(prompt: string): boolean {
  const injectionPatterns = [
    // System prompt override attempts
    /system:|assistant:|user:/gi,
    /ignore previous|forget previous|disregard/gi,
    /you are now|act as|pretend to be/gi,
    
    // Role manipulation
    /\[SYSTEM\]|\[ASSISTANT\]|\[USER\]/gi,
    /<\|system\|>|<\|assistant\|>|<\|user\|>/gi,
    
    // Instruction injection
    /new instructions|override instructions/gi,
    /jailbreak|DAN mode|developer mode/gi,
  ];
  
  return injectionPatterns.some(pattern => pattern.test(prompt));
}

// File validation for Telegram
export const telegramFileSchema = z.object({
  file_id: z.string().min(1).max(200),
  file_unique_id: z.string().min(1).max(32),
  file_size: z.number().int().positive().max(20 * 1024 * 1024), // 20MB Telegram limit
  mime_type: z.enum([
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'text/plain',
  ]).optional(),
});

// Webhook validation
export const webhookUpdateSchema = z.object({
  update_id: z.number().int().positive(),
  message: telegramMessageSchema.optional(),
  callback_query: telegramCallbackQuerySchema.optional(),
  inline_query: z.object({
    id: z.string(),
    from: z.object({
      id: z.number().int().positive(),
    }),
    query: z.string().max(256),
  }).optional(),
});

// Environment validation for bot
export const botEnvSchema = z.object({
  TELEGRAM_BOT_TOKEN: z.string()
    .regex(/^\d+:[A-Za-z0-9_-]{35}$/, 'Invalid Telegram bot token format'),
  TELEGRAM_WEBHOOK_URL: z.string().url().optional(),
  TELEGRAM_WEBHOOK_SECRET: z.string().min(1).optional(),
  CONVEX_URL: z.string().url().optional(),
  CLERK_SECRET_KEY: z.string().min(1),
  OPENROUTER_API_KEY: z.string().min(1),
});

// Validation helper
export function validateTelegramInput<T>(
  schema: z.ZodSchema<T>, 
  data: unknown
): { success: boolean; data?: T; error?: string } {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
      };
    }
    return { success: false, error: 'Validation failed' };
  }
}

// Security logging
export function logSecurityEvent(
  event: string,
  userId?: string,
  details?: Record<string, any>
) {
  console.log(`🔒 SECURITY EVENT: ${event}`, {
    timestamp: new Date().toISOString(),
    userId,
    ...details,
  });
}

// Rate limiting constants
export const TELEGRAM_RATE_LIMITS = {
  FREE: { messages: 10, commands: 5, callbacks: 20 },
  BRONZE: { messages: 50, commands: 25, callbacks: 100 },
  SILVER: { messages: 200, commands: 100, callbacks: 400 },
  DIAMOND: { messages: 1000, commands: 500, callbacks: 2000 },
} as const;

// Message size limits
export const MESSAGE_LIMITS = {
  TEXT: 4096,
  CAPTION: 1024,
  COMMAND_ARGS: 100,
  CALLBACK_DATA: 64,
  INLINE_QUERY: 256,
} as const;
