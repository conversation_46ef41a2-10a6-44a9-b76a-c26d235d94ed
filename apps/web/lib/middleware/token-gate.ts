import { auth, currentUser } from '@clerk/nextjs/server';
import { Connection, PublicKey } from '@solana/web3.js';
import { NextRequest, NextResponse } from 'next/server';
import { UserTier } from '@/lib/ai/providers-openrouter';

// Token balance thresholds for each tier
const TIER_THRESHOLDS = {
  [UserTier.BRONZE]: 20,
  [UserTier.SILVER]: 50,
  [UserTier.DIAMOND]: 100,
};

// Initialize Solana connection
const connection = new Connection(
  process.env.NEXT_PUBLIC_SOLANA_RPC_URL || 'https://api.devnet.solana.com',
);

export async function getUserTier(walletAddress?: string): Promise<UserTier> {
  if (!walletAddress) {
    return UserTier.FREE;
  }

  try {
    const balance = await getTokenBalance(walletAddress);

    if (balance >= TIER_THRESHOLDS[UserTier.DIAMOND]) {
      return UserTier.DIAMOND;
    } else if (balance >= TIER_THRESHOLDS[UserTier.SILVER]) {
      return UserTier.SILVER;
    } else if (balance >= TIER_THRESHOLDS[UserTier.BRONZE]) {
      return UserTier.BRONZE;
    }

    return UserTier.FREE;
  } catch (error) {
    console.error('Error getting user tier:', error);
    return UserTier.FREE;
  }
}

export async function getTokenBalance(walletAddress: string): Promise<number> {
  try {
    const tokenAddress = process.env.NEXT_PUBLIC_BONKAI_TOKEN_ADDRESS;

    if (!tokenAddress) {
      console.warn('BonKai token address not configured');
      return 0;
    }

    // Validate token address format (Solana addresses are 44 characters)
    if (tokenAddress.length !== 44 || tokenAddress.startsWith('http')) {
      console.error('Invalid BonKai token address format:', tokenAddress);
      return 0;
    }

    // Validate wallet address format
    if (walletAddress.length !== 44) {
      console.error('Invalid wallet address format:', walletAddress);
      return 0;
    }

    try {
      const walletPublicKey = new PublicKey(walletAddress);
      const tokenMintPublicKey = new PublicKey(tokenAddress);

      // Get token accounts owned by the wallet for this specific token
      const tokenAccounts = await connection.getTokenAccountsByOwner(
        walletPublicKey,
        { mint: tokenMintPublicKey }
      );

      if (tokenAccounts.value.length === 0) {
        // No token account found, balance is 0
        return 0;
      }

      // Get the balance of the first token account
      const tokenAccountInfo = await connection.getTokenAccountBalance(
        tokenAccounts.value[0].pubkey
      );

      // Return the UI amount (already adjusted for decimals)
      return tokenAccountInfo.value.uiAmount || 0;
    } catch (solanaError) {
      console.error('Solana blockchain query failed:', solanaError);

      // Fallback to development mock data only in development
      if (process.env.NODE_ENV === 'development') {
        console.warn('Using development mock data for token balance');
        const mockBalances: Record<string, number> = {
          mock_bronze_wallet: 25,
          mock_silver_wallet: 60,
          mock_diamond_wallet: 150,
        };
        return mockBalances[walletAddress] || 0;
      }

      return 0;
    }
  } catch (error) {
    console.error('Error getting token balance:', error);
    return 0;
  }
}

export async function tokenGateMiddleware(
  req: NextRequest,
  requiredTier: UserTier = UserTier.FREE,
): Promise<NextResponse | null> {
  const { userId } = await auth();

  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const user = await currentUser();
  const walletAddress = user?.publicMetadata?.walletAddress as
    | string
    | undefined;
  const userTier = await getUserTier(walletAddress);

  // Check if user meets the required tier
  const tierValues = {
    [UserTier.FREE]: 0,
    [UserTier.BRONZE]: 1,
    [UserTier.SILVER]: 2,
    [UserTier.DIAMOND]: 3,
  };

  if (tierValues[userTier] < tierValues[requiredTier]) {
    return NextResponse.json(
      {
        error: 'Insufficient tier access',
        requiredTier,
        userTier,
        message: `This feature requires ${requiredTier} tier. You currently have ${userTier} tier.`,
      },
      { status: 403 },
    );
  }

  // Add tier information to headers for downstream use
  const response = NextResponse.next();
  response.headers.set('x-user-tier', userTier);

  return null;
}

// Rate limiting check
export async function checkRateLimit(
  userId: string,
  userTier: UserTier,
  action: string = 'api_call'
): Promise<{ allowed: boolean; remaining: number; resetAt: number }> {
  const limit = rateLimits[userTier];
  const window = 3600; // 1 hour in seconds
  const key = `rate_limit:${userId}:${action}`;

  try {
    // Try to use Redis if available
    if (process.env.REDIS_URL) {
      const { createClient } = await import('redis');
      const redis = createClient({ url: process.env.REDIS_URL });

      try {
        await redis.connect();

        const current = await redis.incr(key);

        if (current === 1) {
          await redis.expire(key, window);
        }

        const ttl = await redis.ttl(key);
        const resetAt = Date.now() + (ttl * 1000);

        await redis.disconnect();

        return {
          allowed: current <= limit,
          remaining: Math.max(0, limit - current),
          resetAt,
        };
      } catch (redisError) {
        console.error('Redis error, falling back to memory:', redisError);
        await redis.disconnect().catch(() => {});
      }
    }

    // Fallback to in-memory rate limiting (not recommended for production)
    console.warn('Using in-memory rate limiting - not suitable for production');
    return {
      allowed: true,
      remaining: limit - 1,
      resetAt: Date.now() + (window * 1000),
    };
  } catch (error) {
    console.error('Rate limiting error:', error);
    // Fail open - allow the request but log the error
    return {
      allowed: true,
      remaining: limit - 1,
      resetAt: Date.now() + (window * 1000),
    };
  }
}

// Rate limits per tier (requests per hour)
const rateLimits = {
  [UserTier.FREE]: 10,
  [UserTier.BRONZE]: 50,
  [UserTier.SILVER]: 200,
  [UserTier.DIAMOND]: 1000,
};

// Token usage tracking
export async function trackTokenUsage(
  userId: string,
  tokens: number,
): Promise<void> {
  // TODO: Implement token usage tracking in database
  console.log(`User ${userId} used ${tokens} tokens`);
}
