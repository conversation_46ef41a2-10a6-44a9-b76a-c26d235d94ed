import { createUploadthing, type FileRouter } from 'uploadthing/next';
import { UploadThingError } from 'uploadthing/server';
import { auth } from '@clerk/nextjs/server';
import { validateFile } from '@/lib/security/input-validation';

const f = createUploadthing();

export const ourFileRouter = {
  // 🔒 Secure image uploader with validation
  imageUploader: f({
    image: {
      maxFileSize: '4MB',
      maxFileCount: 1,
      // Additional MIME type validation
      allowedFileTypes: ['image/jpeg', 'image/png'],
    }
  })
    .middleware(async ({ req, files }) => {
      // 🔒 Authentication check
      const { userId } = await auth();
      if (!userId) throw new UploadThingError('Unauthorized');

      // 🔒 File validation
      for (const file of files) {
        const validation = await validateFile(file);
        if (!validation.isValid) {
          throw new UploadThingError(`File validation failed: ${validation.error}`);
        }
      }

      console.log('🔒 File upload authorized for user:', userId);
      return { userId };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      console.log('✅ Upload complete for userId:', metadata.userId);
      console.log('📁 File URL:', file.url);
      console.log('📊 File size:', file.size, 'bytes');

      // TODO: Add virus scanning here in production
      // TODO: Add file content validation

      return { uploadedBy: metadata.userId };
    }),

  // 🔒 Secure media uploader for documents and images
  mediaUploader: f({
    image: {
      maxFileSize: '4MB',
      maxFileCount: 1,
      allowedFileTypes: ['image/jpeg', 'image/png'],
    },
    pdf: {
      maxFileSize: '4MB',
      maxFileCount: 1,
      allowedFileTypes: ['application/pdf'],
    },
  })
    .middleware(async ({ req, files }) => {
      // 🔒 Authentication check
      const { userId } = await auth();
      if (!userId) throw new UploadThingError('Unauthorized');

      // 🔒 File validation
      for (const file of files) {
        const validation = await validateFile(file);
        if (!validation.isValid) {
          throw new UploadThingError(`File validation failed: ${validation.error}`);
        }
      }

      console.log('🔒 Media upload authorized for user:', userId);
      return { userId };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      console.log('✅ Media upload complete for userId:', metadata.userId);
      console.log('📁 File URL:', file.url);
      console.log('📊 File size:', file.size, 'bytes');
      console.log('📄 File type:', file.type);

      // TODO: Add virus scanning here in production
      // TODO: Add file content validation
      // TODO: Add file metadata extraction

      return { uploadedBy: metadata.userId };
    }),
} satisfies FileRouter;

export type OurFileRouter = typeof ourFileRouter;
