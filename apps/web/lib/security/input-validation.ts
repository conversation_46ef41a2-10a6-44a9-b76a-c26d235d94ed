// 🔒 Comprehensive input validation and sanitization
import { z } from 'zod';

// Malicious pattern detection
const MALICIOUS_PATTERNS = [
  // XSS patterns
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /javascript:/gi,
  /data:text\/html/gi,
  /vbscript:/gi,
  /on\w+\s*=/gi, // Event handlers like onclick=
  
  // SQL injection patterns
  /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
  /(--|\/\*|\*\/|;)/g,
  
  // Command injection patterns
  /(\||&|;|`|\$\(|\${)/g,
  
  // Path traversal
  /\.\.[\/\\]/g,
  
  // HTML injection
  /<[^>]*>/g,
];

// Content validation functions
export function containsMaliciousPatterns(content: string): boolean {
  return MALICIOUS_PATTERNS.some(pattern => pattern.test(content));
}

export function sanitizeText(text: string): string {
  return text
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
}

// Validation schemas
export const messageSchema = z.object({
  content: z.string()
    .min(1, 'Message cannot be empty')
    .max(2000, 'Message too long')
    .refine(
      (content) => !containsMaliciousPatterns(content),
      'Message contains prohibited content'
    ),
  attachments: z.array(z.object({
    url: z.string().url('Invalid attachment URL'),
    type: z.enum(['image/jpeg', 'image/png', 'application/pdf'], {
      errorMap: () => ({ message: 'Invalid file type' })
    }),
    size: z.number().max(4 * 1024 * 1024, 'File too large (max 4MB)'),
    name: z.string().min(1).max(255, 'Invalid filename'),
  })).max(5, 'Too many attachments'),
});

export const walletAddressSchema = z.string()
  .length(44, 'Invalid Solana wallet address length')
  .regex(/^[1-9A-HJ-NP-Za-km-z]{44}$/, 'Invalid Solana wallet address format');

export const tokenAddressSchema = z.string()
  .length(44, 'Invalid Solana token address length')
  .regex(/^[1-9A-HJ-NP-Za-km-z]{44}$/, 'Invalid Solana token address format');

export const chatTitleSchema = z.string()
  .min(1, 'Title cannot be empty')
  .max(100, 'Title too long')
  .refine(
    (title) => !containsMaliciousPatterns(title),
    'Title contains prohibited content'
  );

export const userTierSchema = z.enum(['FREE', 'BRONZE', 'SILVER', 'DIAMOND']);

// File validation
export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  sanitizedName?: string;
}

export async function validateFile(file: File): Promise<FileValidationResult> {
  // Check file size
  if (file.size > 4 * 1024 * 1024) {
    return { isValid: false, error: 'File too large (max 4MB)' };
  }

  // Check file name
  const sanitizedName = sanitizeFileName(file.name);
  if (!sanitizedName) {
    return { isValid: false, error: 'Invalid filename' };
  }

  // Check file type
  const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
  if (!allowedTypes.includes(file.type)) {
    return { isValid: false, error: 'Invalid file type' };
  }

  // Additional security checks could be added here:
  // - File content validation
  // - Virus scanning
  // - Magic number verification

  return { 
    isValid: true, 
    sanitizedName 
  };
}

function sanitizeFileName(fileName: string): string | null {
  // Remove path traversal attempts
  const sanitized = fileName
    .replace(/[\/\\:*?"<>|]/g, '') // Remove dangerous characters
    .replace(/\.\./g, '') // Remove path traversal
    .trim();

  // Check if filename is still valid
  if (sanitized.length === 0 || sanitized.length > 255) {
    return null;
  }

  return sanitized;
}

// Rate limiting validation
export const rateLimitSchema = z.object({
  userId: z.string().min(1, 'User ID required'),
  action: z.string().min(1, 'Action required'),
  tier: userTierSchema,
});

// API request validation
export const apiRequestSchema = z.object({
  method: z.enum(['GET', 'POST', 'PUT', 'PATCH', 'DELETE']),
  headers: z.record(z.string()).optional(),
  body: z.any().optional(),
});

// Telegram message validation
export const telegramMessageSchema = z.object({
  text: z.string()
    .min(1, 'Message cannot be empty')
    .max(4096, 'Telegram message too long') // Telegram limit
    .refine(
      (text) => !containsMaliciousPatterns(text),
      'Message contains prohibited content'
    ),
  chatId: z.number().int().positive('Invalid chat ID'),
  userId: z.number().int().positive('Invalid user ID'),
});

// Environment validation
export const envSchema = z.object({
  CLERK_SECRET_KEY: z.string().min(1, 'Clerk secret key required'),
  OPENROUTER_API_KEY: z.string().min(1, 'OpenRouter API key required'),
  TELEGRAM_BOT_TOKEN: z.string().min(1, 'Telegram bot token required'),
  NEXT_PUBLIC_BONKAI_TOKEN_ADDRESS: tokenAddressSchema,
  CONVEX_DEPLOYMENT: z.string().url('Invalid Convex deployment URL'),
  UPLOADTHING_SECRET: z.string().min(1, 'UploadThing secret required'),
});

// Validation middleware helper
export function validateInput<T>(schema: z.ZodSchema<T>, data: unknown): {
  success: boolean;
  data?: T;
  error?: string;
} {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: error.errors.map(e => e.message).join(', ') 
      };
    }
    return { 
      success: false, 
      error: 'Validation failed' 
    };
  }
}

// Security headers
export const SECURITY_HEADERS = {
  'Content-Security-Policy': `
    default-src 'self';
    script-src 'self' 'unsafe-eval' 'unsafe-inline' *.vercel.app;
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: blob: *.uploadthing.com *.convex.cloud;
    connect-src 'self' *.convex.cloud *.openrouter.ai wss:;
    frame-ancestors 'none';
    base-uri 'self';
    form-action 'self';
  `.replace(/\s+/g, ' ').trim(),
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'X-XSS-Protection': '1; mode=block',
};
