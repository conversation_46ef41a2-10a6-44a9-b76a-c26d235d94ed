# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Quick Commands

```bash
# Development
bun dev                     # Start all apps in development
bun web:dev                 # Start only web app
bun bot:dev                 # Start only Telegram bot
bun build                   # Build all apps
bun start                   # Start production servers

# Code Quality
bun lint                    # Run linting with Biome
bun format                  # Format code with Biome

# Convex Database
bun convex:dev             # Start Convex development server
bun convex:deploy          # Deploy Convex functions
bun convex:dashboard       # Open Convex dashboard
bun convex:auth            # Manage Convex authentication

# Telegram Bot
bun bot:build              # Build Telegram bot
bun bot:start              # Start production bot
bun bot:webhook            # Setup webhook
bun bot:deploy             # Deploy bot to production

# Testing
bun test                   # Run Playwright E2E tests (sets PLAYWRIGHT=True)
```

## 🏗️ Architecture Overview

This is the **BonKai Web3 Ecosystem** - a comprehensive platform combining AI chatbots with Web3 features on Solana. The project uses a Turborepo monorepo structure:

- **Frontend**: Next.js 15 App Router with React Server Components and PPR enabled
- **Database**: Convex for real-time database with tiered user system
- **Authentication**: Clerk with Web3 wallet support (Solana wallets)
- **AI Models**: OpenRouter provider with tier-based model access (Gemini 2.0 Flash, o3-mini)
- **File Storage**: Uploadthing for file uploads
- **Blockchain**: Solana integration for NFTs, tokens, and staking
- **Multi-platform**: Web app + Telegram bot with grammY framework
- **Package Manager**: Bun with workspace configuration
- **Monorepo**: Turborepo for managing multiple apps and packages

## 📁 Monorepo Structure

```
bonkai/
├── apps/
│   ├── web/                # Next.js web application (@bonkai/web)
│   ├── telegram-bot/       # Telegram bot with grammY (@bonkai/telegram-bot)
│   └── api/               # Shared API services (@bonkai/api)
├── packages/
│   ├── ui/                # Shared UI components (@bonkai/ui)
│   ├── auth/              # Authentication utilities (@bonkai/auth)
│   ├── ai/                # AI provider integration (@bonkai/ai)
│   ├── blockchain/        # Solana blockchain utilities (@bonkai/blockchain)
│   └── types/             # Shared TypeScript types (@bonkai/types)
├── convex/                # Convex database functions and schema
├── docs/                  # Project documentation
├── turbo.json             # Turborepo configuration
└── tsconfig.base.json     # Base TypeScript config

apps/web/
├── app/
│   ├── (auth)/            # Authentication pages (login, register, link)
│   ├── (chat)/            # Chat interface with artifacts
│   └── api/               # API routes (Telegram, wallet, uploadthing)
├── components/            # React components including Web3 components
├── artifacts/             # AI-generated artifacts (code, text, image, sheet)
├── lib/                   # Web-specific utilities (AI, auth, convex)
├── hooks/                 # React hooks
├── tests/                 # Playwright E2E tests
└── public/                # Static assets
```

## 🔧 Key Configuration Files

- **`package.json`**: Root monorepo config using Bun workspaces
- **`turbo.json`**: Turborepo pipeline configuration
- **`biome.jsonc`**: Code formatting and linting configuration
- **`convex/schema.ts`**: Convex database schema definition
- **`next.config.ts`**: Next.js configuration
- **`playwright.config.ts`**: E2E testing configuration

## 🤖 AI Implementation Details

### Provider Setup
- Uses **OpenRouter** via custom provider with fallback to xAI models
- Model configuration in `lib/ai/providers-openrouter.ts` (active) and `lib/ai/providers.ts` (xAI fallback)
- Test environment uses mock models from `models.test.ts`
- Tier-based model access with token limits and rate limiting

### Available Models by Tier
- **FREE**: `google/gemini-2.0-flash-exp:free` (10K tokens/month, 10 req/hour)
- **BRONZE**: Gemini + limited premium access (100K tokens/month, 50 req/hour)
- **SILVER**: Full premium model access (500K tokens/month, 200 req/hour)
- **DIAMOND**: Full access + reasoning models (2M tokens/month, 1000 req/hour)

### Model Types
- **chat-model**: Gemini 2.0 Flash for general conversation
- **chat-model-reasoning**: o3-mini with reasoning middleware (DIAMOND tier)
- **premium-model**: GPT-4o for complex tasks (SILVER+ tiers)
- **image-model**: DALL-E 3 for image generation
- **video-model**: Gemini Pro Vision for video processing

### AI Tools
Located in `lib/ai/tools/`:
- `get-weather.ts`: Weather information
- `create-document.ts`: Document creation
- `update-document.ts`: Document updates
- `request-suggestions.ts`: Suggestion generation

## 💾 Database Schema

Uses **Convex** as the backend database with real-time updates:

### Core Tables
- `users`: User accounts with Clerk ID, wallet linking, tier system
- `chats`: Chat sessions with visibility settings (public/private)
- `messages`: Chat messages with parts, attachments, and token tracking
- `votes`: Message voting system
- `documents`: AI-generated artifacts (text, code, image, sheet)
- `suggestions`: Document editing suggestions
- `streams`: Chat stream tracking for real-time updates

### Web3 & Blockchain Tables
- `stakingPositions`: User staking data and aura points
- `tokenUsage`: Monthly token consumption tracking
- `rateLimits`: Request rate limiting per user tier
- `bankaiMoves`: Special Web3 game actions

### Telegram Integration
- `telegramUsers`: Links Telegram accounts to main users
- Cross-platform user synchronization

## 🔐 Authentication

Uses **Clerk** with Web3 integration:
- Email/password authentication
- Solana wallet linking and support
- Tier-based access control (FREE, BRONZE, SILVER, DIAMOND)
- Cross-platform auth between web and Telegram
- Session management via Clerk with Convex user sync
- Protected routes via Clerk middleware

## 🎨 Styling & Theming

- **Tailwind CSS** with custom theme configuration
- **shadcn/ui** components with custom color palette
- **Biome** for code formatting instead of Prettier
- Custom theme variables in `app/globals.css`
- OKLCH color space for better color management

## 📦 File Storage

- **Uploadthing** for file uploads and management
- Multimodal input supports images and documents
- File upload configuration in `app/api/uploadthing/`
- Preview components for different file types
- Integrated with AI artifact generation

## 🧪 Testing

- **Playwright** for E2E testing with 240s timeout
- Tests organized in `tests/e2e/` and `tests/routes/` directories
- Page objects pattern in `tests/pages/`
- Test environment detection via `PLAYWRIGHT` env variable
- Mock AI models for testing in `models.test.ts`
- Separate test projects for E2E and route testing

## 🌐 Web3 Integration

### Solana Blockchain
- **Network**: Configurable (devnet/mainnet-beta via `NEXT_PUBLIC_SOLANA_NETWORK`)
- **Wallet Support**: Full Solana wallet adapter integration
- **Custom Token**: BonKai token with configurable address
- **Staking System**: On-chain staking with aura points and tier progression
- **NFTs**: Integration for NFT-based features

### User Tier System
- **FREE**: Basic access with Gemini 2.0 Flash (10K tokens/month)
- **BRONZE**: Enhanced access (100K tokens/month)
- **SILVER**: Premium models access (500K tokens/month)
- **DIAMOND**: Full access including reasoning models (2M tokens/month)

### Web3 Components
Located in `components/web3/`:
- Wallet connection and status
- Staking position management
- Token balance and usage tracking
- Transaction history and status
- Tier-based UI and benefits display

## 📱 Telegram Bot

Built with **grammY** framework:
- Real-time sync with web app via Convex
- User authentication and account linking
- Rate limiting and tier-based access
- Webhook and polling support
- Conversation handling and menu system

### Bot Commands
- Account linking with web platform
- AI chat functionality
- Wallet integration status
- Usage and tier information

## 🔍 Development Tips

### Common Patterns
- Use Server Components by default, Client Components only when needed
- Database queries centralized in `lib/convex/queries.ts` and `convex/` functions
- AI streaming responses use `streamText` from AI SDK
- Tier-based model selection via `getModelForTask()` function
- Real-time updates via Convex subscriptions

### Code Quality
- TypeScript strict mode enabled
- Biome handles both linting and formatting (configured in `biome.jsonc`)
- No unused imports or variables allowed
- Prefer named exports over default exports
- 2-space indentation, 80-character line width

### Environment Variables
Required variables in `.env.local` (see `.env.example`):
- Clerk authentication keys
- OpenRouter AI API key
- Convex deployment URL
- Uploadthing configuration
- Solana network settings
- Telegram bot token (for bot functionality)

## 🚨 Important Notes

- Use `bun convex:dev` for database development server
- Test environment uses mock AI models (controlled by `PLAYWRIGHT` env var)
- Biome configuration in `biome.jsonc` overrides default settings
- Playwright config uses `pnpm dev` command - update if needed
- Follow tier-based access patterns for AI features
- Maintain cross-platform sync between web and Telegram