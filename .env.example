# 🔒 BonKai Environment Configuration Template
# 📝 Copy this file to .env.local and fill in your actual values
# ⚠️  NEVER commit .env.local to version control

# Clerk Authentication (PRIMARY - Use this for auth)
# Get your keys from https://dashboard.clerk.com
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
CLERK_SECRET_KEY=sk_test_your_secret_key_here
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/login
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/register
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/

# OpenRouter AI
# Get your API key from https://openrouter.ai/keys
OPENROUTER_API_KEY=sk-or-v1-your_openrouter_api_key_here

# Convex Database
# Get started at https://www.convex.dev/
# Note: These will be auto-generated when running convex dev
CONVEX_DEPLOYMENT=https://your-deployment.convex.cloud
NEXT_PUBLIC_CONVEX_URL=https://your-deployment.convex.site

# Uploadthing File Storage
# Get your keys from https://uploadthing.com/dashboard
UPLOADTHING_SECRET=sk_live_your_uploadthing_secret_here
UPLOADTHING_APP_ID=your_uploadthing_app_id_here

# Solana Configuration
# Use 'devnet' for development, 'mainnet-beta' for production
NEXT_PUBLIC_SOLANA_NETWORK=devnet
# Optional: Use a custom RPC endpoint (e.g., Helius, QuickNode)
NEXT_PUBLIC_SOLANA_RPC_URL=https://api.devnet.solana.com

# BonKai Token Configuration
# ⚠️  IMPORTANT: Must be a valid Solana token address (44 characters)
# Example for SOL: So11111111111111111111111111111111111111112
NEXT_PUBLIC_BONKAI_TOKEN_ADDRESS=your_valid_solana_token_address_here

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_WEBHOOK_URL=https://your-domain.com/api/telegram/webhook
TELEGRAM_WEBHOOK_SECRET=your_webhook_secret_here

# WebSocket Server
WS_PORT=3001

# Security Configuration
# Generate a random 32-character string for JWT signing
JWT_SECRET=your_jwt_secret_here

# Rate Limiting (Redis) - Optional but recommended for production
REDIS_URL=redis://localhost:6379

# Environment Configuration
NODE_ENV=development
NEXTAUTH_URL=http://localhost:3000

# 🗑️  DEPRECATED - Will be removed (DO NOT USE)
# These are legacy from old authentication system
# XAI_API_KEY=... (Use OPENROUTER_API_KEY instead)
# POSTGRES_URL=... (Use Convex instead)
# AUTH_SECRET=... (Use Clerk instead)
